import { dataSource } from './src/configs/typeorm.datasource';
import { ProposalRepository } from './src/repositories/proposal.repository';
import { TargetRepository } from './src/repositories/target.repository';

async function testAgreeField() {
  try {
    await dataSource.initialize();
    console.log('📦 Database connected.');

    const proposalRepo = new ProposalRepository(
      dataSource.getRepository(require('./src/entities/proposals.entity').ProposalEntity),
      new TargetRepository(
        dataSource.getRepository(require('./src/entities/targets.entity').TargetEntity),
        null as any
      )
    );

    // Test findAllProposals
    console.log('🔍 Testing findAllProposals...');
    const result = await proposalRepo.findAllProposals({}, 1, 2);
    
    console.log(`Found ${result.data.length} proposals`);
    
    result.data.forEach((proposal, index) => {
      console.log(`\n📋 Proposal ${index + 1}:`);
      console.log(`  UID: ${proposal.uid}`);
      console.log(`  Status: ${proposal.status}`);
      console.log(`  Targets: ${proposal.targets?.length || 0}`);
      
      proposal.targets?.forEach((target, targetIndex) => {
        console.log(`    🎯 Target ${targetIndex + 1}:`);
        console.log(`      UID: ${target.uid}`);
        console.log(`      Weight: ${target.weight}`);
        console.log(`      Scope: ${target.scope}`);
        console.log(`      Target Types: ${target.targetTypes?.length || 0}`);
        
        target.targetTypes?.forEach((targetType, typeIndex) => {
          console.log(`        📝 Target Type ${typeIndex + 1}:`);
          console.log(`          Type: ${targetType.type}`);
          console.log(`          Agree: ${targetType.agree}`);
        });
        
        if (target.children?.length > 0) {
          console.log(`      Children: ${target.children.length}`);
          target.children.forEach((child, childIndex) => {
            console.log(`        🎯 Child ${childIndex + 1}:`);
            console.log(`          UID: ${child.uid}`);
            console.log(`          Target Types: ${child.targetTypes?.length || 0}`);
            
            child.targetTypes?.forEach((childTargetType, childTypeIndex) => {
              console.log(`            📝 Child Target Type ${childTypeIndex + 1}:`);
              console.log(`              Type: ${childTargetType.type}`);
              console.log(`              Agree: ${childTargetType.agree}`);
            });
          });
        }
      });
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await dataSource.destroy();
    console.log('✅ Database connection closed.');
  }
}

testAgreeField();
