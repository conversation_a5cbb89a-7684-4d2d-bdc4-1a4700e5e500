// Example usage of the new getAgreeStatusByUid method

import { TargetTypeRepository } from './src/repositories/target-type.repository';
import { dataSource } from './src/configs/typeorm.datasource';

async function exampleUsage() {
  await dataSource.initialize();
  
  const targetTypeRepo = new TargetTypeRepository(
    dataSource.getRepository(require('./src/entities/target-types.entity').TargetTypeEntity)
  );

  // Example 1: Get agree status for a specific target type
  const targetTypeUid = 'some-target-type-uid';
  const agreeStatus = await targetTypeRepo.getAgreeStatusByUid(targetTypeUid);
  
  if (agreeStatus === true) {
    console.log('Target type is agreed upon');
  } else if (agreeStatus === false) {
    console.log('Target type is not agreed upon');
  } else if (agreeStatus === null) {
    console.log('Target type not found or agree status is null');
  }

  // Example 2: Batch processing with error handling
  const targetTypeUids = ['uid1', 'uid2', 'uid3'];
  
  for (const uid of targetTypeUids) {
    try {
      const status = await targetTypeRepo.getAgreeStatusByUid(uid);
      console.log(`Target Type ${uid}: ${status}`);
    } catch (error) {
      console.error(`Error fetching agree status for ${uid}:`, error);
    }
  }

  await dataSource.destroy();
}

// Usage in a service or controller
class TargetTypeService {
  constructor(private targetTypeRepo: TargetTypeRepository) {}

  async checkAgreementStatus(targetTypeUid: string): Promise<string> {
    const agreeStatus = await this.targetTypeRepo.getAgreeStatusByUid(targetTypeUid);
    
    switch (agreeStatus) {
      case true:
        return 'AGREED';
      case false:
        return 'NOT_AGREED';
      case null:
        return 'UNKNOWN';
      default:
        return 'INVALID';
    }
  }

  async getAgreementStatistics(targetTypeUids: string[]): Promise<{
    agreed: number;
    notAgreed: number;
    unknown: number;
  }> {
    let agreed = 0;
    let notAgreed = 0;
    let unknown = 0;

    for (const uid of targetTypeUids) {
      const status = await this.targetTypeRepo.getAgreeStatusByUid(uid);
      
      if (status === true) {
        agreed++;
      } else if (status === false) {
        notAgreed++;
      } else {
        unknown++;
      }
    }

    return { agreed, notAgreed, unknown };
  }
}
