// Updated example showing how to use the modified getAgreeStatusByUids method
// with the new object-based parameter structure

import { ProposalRepository, TargetTypeRepository } from './src/repositories';
import { ProposalEntity } from './src/entities/proposals.entity';

/**
 * Updated API service that enriches proposal data with fresh agree values
 * using the new getAgreeStatusByUids method signature
 */
export class ProposalApiService {
  constructor(
    private proposalRepository: ProposalRepository,
    private targetTypeRepository: TargetTypeRepository
  ) {}

  /**
   * Get all proposals with fresh agree field values
   */
  async getAllProposalsWithFreshAgreeValues(filters: any, pageNumber: number, pageSize: number) {
    // 1. Get proposals from domain repository (returns raw data with UIDs)
    const result = await this.proposalRepository.findAllProposals(filters, pageNumber, pageSize);
    
    // 2. Collect target type identifiers (both uidTarget and uid)
    const targetTypeIdentifiers = this.collectTargetTypeIdentifiers(result.data);
    
    // 3. Fetch fresh agree values using the new method signature
    const agreeResults = await this.targetTypeRepository.getAgreeStatusByUids(targetTypeIdentifiers);
    
    // 4. Enrich the proposals with fresh agree values
    const enrichedProposals = this.enrichProposalsWithAgreeValues(result.data, agreeResults);
    
    return {
      ...result,
      data: enrichedProposals
    };
  }

  /**
   * Get a single proposal with fresh agree field values
   */
  async getProposalByUidWithFreshAgreeValues(uid: string) {
    // 1. Get proposal from domain repository
    const proposal = await this.proposalRepository.findByUid(uid);
    
    if (!proposal) {
      return null;
    }
    
    // 2. Collect target type identifiers
    const targetTypeIdentifiers = this.collectTargetTypeIdentifiers([proposal]);
    
    // 3. Fetch fresh agree values
    const agreeResults = await this.targetTypeRepository.getAgreeStatusByUids(targetTypeIdentifiers);
    
    // 4. Enrich the proposal
    const enrichedProposals = this.enrichProposalsWithAgreeValues([proposal], agreeResults);
    
    return enrichedProposals[0];
  }

  /**
   * Collects target type identifiers (uidTarget and uid pairs) from proposals
   */
  private collectTargetTypeIdentifiers(
    proposals: ProposalEntity[]
  ): Array<{ uidTarget: string; uid: string }> {
    const identifiers: Array<{ uidTarget: string; uid: string }> = [];
    
    proposals.forEach((proposal) => {
      proposal.targets?.forEach((target) => {
        // Parent target types
        target.targetTypes?.forEach((targetType) => {
          if (targetType.uid && targetType.uidTarget) {
            identifiers.push({
              uidTarget: targetType.uidTarget,
              uid: targetType.uid
            });
          }
        });
        
        // Child target types
        target.children?.forEach((child) => {
          child.targetTypes?.forEach((childTargetType) => {
            if (childTargetType.uid && childTargetType.uidTarget) {
              identifiers.push({
                uidTarget: childTargetType.uidTarget,
                uid: childTargetType.uid
              });
            }
          });
        });
      });
    });

    return identifiers;
  }

  /**
   * Enriches proposals with fresh agree values using the new result format
   */
  private enrichProposalsWithAgreeValues(
    proposals: ProposalEntity[], 
    agreeResults: Array<{ uidTarget: string; uid: string; agree?: boolean | null }>
  ): ProposalEntity[] {
    // Create a map for quick lookup: "uidTarget:uid" -> agree value
    const agreeMap = new Map<string, boolean | null>();
    agreeResults.forEach((result) => {
      const key = `${result.uidTarget}:${result.uid}`;
      agreeMap.set(key, result.agree ?? null);
    });

    proposals.forEach((proposal) => {
      proposal.targets?.forEach((target) => {
        // Update parent target types
        target.targetTypes?.forEach((targetType) => {
          if (targetType.uid && targetType.uidTarget) {
            const key = `${targetType.uidTarget}:${targetType.uid}`;
            if (agreeMap.has(key)) {
              targetType.agree = agreeMap.get(key);
            }
          }
        });
        
        // Update child target types
        target.children?.forEach((child) => {
          child.targetTypes?.forEach((childTargetType) => {
            if (childTargetType.uid && childTargetType.uidTarget) {
              const key = `${childTargetType.uidTarget}:${childTargetType.uid}`;
              if (agreeMap.has(key)) {
                childTargetType.agree = agreeMap.get(key);
              }
            }
          });
        });
      });
    });

    return proposals;
  }
}

/**
 * Updated utility function for enrichment
 */
export class ProposalEnrichmentUtil {
  static async enrichWithFreshAgreeValues(
    proposals: ProposalEntity[],
    targetTypeRepository: TargetTypeRepository
  ): Promise<ProposalEntity[]> {
    // Collect target type identifiers
    const identifiers: Array<{ uidTarget: string; uid: string }> = [];
    
    proposals.forEach((proposal) => {
      proposal.targets?.forEach((target) => {
        // Parent target types
        target.targetTypes?.forEach((tt) => {
          if (tt.uid && tt.uidTarget) {
            identifiers.push({ uidTarget: tt.uidTarget, uid: tt.uid });
          }
        });
        
        // Child target types
        target.children?.forEach((child) => {
          child.targetTypes?.forEach((ctt) => {
            if (ctt.uid && ctt.uidTarget) {
              identifiers.push({ uidTarget: ctt.uidTarget, uid: ctt.uid });
            }
          });
        });
      });
    });

    if (identifiers.length === 0) {
      return proposals;
    }

    // Fetch fresh values using new method signature
    const agreeResults = await targetTypeRepository.getAgreeStatusByUids(identifiers);

    // Create lookup map
    const agreeMap = new Map<string, boolean | null>();
    agreeResults.forEach((result) => {
      const key = `${result.uidTarget}:${result.uid}`;
      agreeMap.set(key, result.agree ?? null);
    });

    // Update proposals
    proposals.forEach((proposal) => {
      proposal.targets?.forEach((target) => {
        target.targetTypes?.forEach((tt) => {
          if (tt.uid && tt.uidTarget) {
            const key = `${tt.uidTarget}:${tt.uid}`;
            if (agreeMap.has(key)) {
              tt.agree = agreeMap.get(key);
            }
          }
        });
        
        target.children?.forEach((child) => {
          child.targetTypes?.forEach((ctt) => {
            if (ctt.uid && ctt.uidTarget) {
              const key = `${ctt.uidTarget}:${ctt.uid}`;
              if (agreeMap.has(key)) {
                ctt.agree = agreeMap.get(key);
              }
            }
          });
        });
      });
    });

    return proposals;
  }
}

/**
 * Usage example with new method signature:
 * 
 * // In your API layer
 * const proposals = await proposalRepository.findAllProposals(filters, page, size);
 * 
 * // Collect identifiers with both uidTarget and uid
 * const identifiers = proposals.data.flatMap(proposal =>
 *   proposal.targets?.flatMap(target => [
 *     ...(target.targetTypes?.map(tt => ({ uidTarget: tt.uidTarget, uid: tt.uid })) || []),
 *     ...(target.children?.flatMap(child => 
 *       child.targetTypes?.map(ctt => ({ uidTarget: ctt.uidTarget, uid: ctt.uid })) || []
 *     ) || [])
 *   ]) || []
 * ).filter(id => id.uidTarget && id.uid);
 * 
 * // Get agree values using new method
 * const agreeResults = await targetTypeRepository.getAgreeStatusByUids(identifiers);
 * 
 * // Enrich proposals
 * const enrichedProposals = await ProposalEnrichmentUtil.enrichWithFreshAgreeValues(
 *   proposals.data, 
 *   targetTypeRepository
 * );
 * 
 * return { ...proposals, data: enrichedProposals };
 */
