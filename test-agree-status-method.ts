import { dataSource } from './src/configs/typeorm.datasource';
import { TargetTypeRepository } from './src/repositories/target-type.repository';

async function testGetAgreeStatusByUid() {
  try {
    await dataSource.initialize();
    console.log('📦 Database connected.');

    const targetTypeRepo = new TargetTypeRepository(
      dataSource.getRepository(require('./src/entities/target-types.entity').TargetTypeEntity)
    );

    // First, let's get some target type UIDs to test with
    console.log('🔍 Finding target types to test...');
    const sampleTargetTypes = await targetTypeRepo.repository
      .createQueryBuilder('tt')
      .select(['tt.uid', 'tt.type', 'tt.agree'])
      .limit(5)
      .getMany();

    console.log(`Found ${sampleTargetTypes.length} target types for testing:`);
    sampleTargetTypes.forEach((tt, i) => {
      console.log(`  ${i + 1}. UID: ${tt.uid}, Type: ${tt.type}, Agree: ${tt.agree}`);
    });

    // Test the new method with each sample
    console.log('\n🧪 Testing getAgreeStatusByUid method:');
    for (const targetType of sampleTargetTypes) {
      const agreeStatus = await targetTypeRepo.getAgreeStatusByUid(targetType.uid);
      console.log(`  UID: ${targetType.uid}`);
      console.log(`    Expected: ${targetType.agree}`);
      console.log(`    Actual:   ${agreeStatus}`);
      console.log(`    Match:    ${targetType.agree === agreeStatus ? '✅' : '❌'}`);
      console.log('');
    }

    // Test with a non-existent UID
    console.log('🧪 Testing with non-existent UID:');
    const nonExistentUid = '00000000-0000-0000-0000-000000000000';
    const nullResult = await targetTypeRepo.getAgreeStatusByUid(nonExistentUid);
    console.log(`  UID: ${nonExistentUid}`);
    console.log(`  Result: ${nullResult}`);
    console.log(`  Is null: ${nullResult === null ? '✅' : '❌'}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await dataSource.destroy();
    console.log('✅ Database connection closed.');
  }
}

testGetAgreeStatusByUid();
