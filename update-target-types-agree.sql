-- SQL script to update existing target_types records with random agree values
-- This will set agree to true for ~40%, false for ~40%, and leave ~20% as null

UPDATE [tsc-dev].[target_types] 
SET agree = CASE 
    WHEN ABS(CHECKSUM(NEWID())) % 10 < 4 THEN 1  -- 40% true
    WHEN ABS(CHECKSUM(NEWID())) % 10 < 8 THEN 0  -- 40% false  
    ELSE NULL                                     -- 20% null
END
WHERE agree IS NULL;
