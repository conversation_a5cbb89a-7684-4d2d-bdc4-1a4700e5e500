import { IsNull, type DataSource } from 'typeorm';
import { TargetEntity, TargetTypeEntity } from '../../entities';
import { TargetType } from '../../enums';

export const runTargetTypesSeed = async (dataSource: DataSource) => {
  const targetRepo = dataSource.getRepository(TargetEntity);
  const targetTypeRepo = dataSource.getRepository(TargetTypeEntity);

  console.log('🚀 Seeding Target Types...');

  const parentTargets = await targetRepo.find({
    where: {
      uidParentTarget: IsNull()
    }
  });

  console.log(`Found ${parentTargets.length} parent targets`);

  const targetTypes: Partial<TargetTypeEntity>[] = [];

  for (const parentTarget of parentTargets) {
    // Generate random agree value for testing (true, false, or null)
    const randomValue = Math.random();
    let agree: boolean | undefined;
    if (randomValue < 0.4) {
      agree = true;
    } else if (randomValue < 0.8) {
      agree = false;
    } else {
      agree = undefined; // This will be null in the database
    }

    targetTypes.push({
      uidTarget: parentTarget.uid,
      type: TargetType.PROPOSAL,
      agree: agree
    });
  }

  await targetTypeRepo.save(targetTypes);

  console.log(`✅ ${targetTypes.length} Target Types seeded for parent targets!`);
};
