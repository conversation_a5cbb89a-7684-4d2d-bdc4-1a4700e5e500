-- SQL script to set up test data for the getAgreeStatusByUid method
-- This ensures we have target types with different agree values for testing

-- Update some records to have true
UPDATE TOP(3) [tsc-dev].[target_types] 
SET agree = 1 
WHER<PERSON> agree IS NULL OR agree != 1;

-- Update some records to have false  
UPDATE TOP(3) [tsc-dev].[target_types] 
SET agree = 0 
WHERE agree IS NULL OR agree != 0;

-- Leave some records as null
UPDATE TOP(2) [tsc-dev].[target_types] 
SET agree = NULL 
WHERE agree IS NOT NULL;

-- Check the distribution
SELECT 
    agree,
    COUNT(*) as count
FROM [tsc-dev].[target_types]
GROUP BY agree
ORDER BY agree;

-- Show some sample records for testing
SELECT TOP 5 
    uid,
    type,
    agree,
    uidTarget
FROM [tsc-dev].[target_types]
ORDER BY agree DESC, uid;
