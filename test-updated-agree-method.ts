import { dataSource } from './src/configs/typeorm.datasource';
import { TargetTypeRepository } from './src/repositories/target-type.repository';

async function testUpdatedAgreeMethod() {
  try {
    await dataSource.initialize();
    console.log('📦 Database connected.');

    const targetTypeRepo = new TargetTypeRepository(
      dataSource.getRepository(require('./src/entities/target-types.entity').TargetTypeEntity)
    );

    // Test 1: Get some sample target types to test with
    console.log('🔍 Getting sample target types...');
    const sampleTargetTypes = await targetTypeRepo.repository
      .createQueryBuilder('tt')
      .select(['tt.uid', 'tt.uidTarget', 'tt.type', 'tt.agree'])
      .limit(5)
      .getMany();

    console.log(`Found ${sampleTargetTypes.length} target types for testing:`);
    sampleTargetTypes.forEach((tt, i) => {
      console.log(`  ${i + 1}. UID: ${tt.uid}, uidTarget: ${tt.uidTarget}, Type: ${tt.type}, Agree: ${tt.agree}`);
    });

    // Test 2: Test the new method with valid identifiers
    console.log('\n🧪 Testing getAgreeStatusByUids with new signature...');
    const identifiers = sampleTargetTypes.map(tt => ({
      uidTarget: tt.uidTarget,
      uid: tt.uid
    }));

    console.log('Input identifiers:');
    identifiers.forEach((id, i) => {
      console.log(`  ${i + 1}. uidTarget: ${id.uidTarget}, uid: ${id.uid}`);
    });

    const results = await targetTypeRepo.getAgreeStatusByUids(identifiers);

    console.log('\nResults from getAgreeStatusByUids:');
    results.forEach((result, i) => {
      const original = sampleTargetTypes.find(tt => tt.uidTarget === result.uidTarget && tt.uid === result.uid);
      console.log(`  ${i + 1}. uidTarget: ${result.uidTarget}, uid: ${result.uid}, agree: ${result.agree}`);
      console.log(`      Original agree value: ${original?.agree}`);
      console.log(`      Match: ${original?.agree === result.agree ? '✅' : '❌'}`);
    });

    // Test 3: Test with empty array
    console.log('\n🧪 Testing with empty array...');
    const emptyResults = await targetTypeRepo.getAgreeStatusByUids([]);
    console.log(`Empty array result: ${JSON.stringify(emptyResults)} (should be empty array)`);

    // Test 4: Test with non-existent identifiers
    console.log('\n🧪 Testing with non-existent identifiers...');
    const nonExistentIdentifiers = [
      { uidTarget: '00000000-0000-0000-0000-000000000001', uid: '00000000-0000-0000-0000-000000000002' },
      { uidTarget: '00000000-0000-0000-0000-000000000003', uid: '00000000-0000-0000-0000-000000000004' }
    ];

    const nonExistentResults = await targetTypeRepo.getAgreeStatusByUids(nonExistentIdentifiers);
    console.log('Non-existent identifiers results:');
    nonExistentResults.forEach((result, i) => {
      console.log(`  ${i + 1}. uidTarget: ${result.uidTarget}, uid: ${result.uid}, agree: ${result.agree} (should be null)`);
    });

    // Test 5: Test with mixed existing and non-existing identifiers
    console.log('\n🧪 Testing with mixed existing and non-existing identifiers...');
    const mixedIdentifiers = [
      ...identifiers.slice(0, 2), // First 2 existing
      ...nonExistentIdentifiers.slice(0, 1) // 1 non-existing
    ];

    const mixedResults = await targetTypeRepo.getAgreeStatusByUids(mixedIdentifiers);
    console.log('Mixed identifiers results:');
    mixedResults.forEach((result, i) => {
      console.log(`  ${i + 1}. uidTarget: ${result.uidTarget}, uid: ${result.uid}, agree: ${result.agree}`);
    });

    // Test 6: Performance test
    console.log('\n⚡ Performance test...');
    const performanceIdentifiers = identifiers.concat(identifiers).concat(identifiers); // Triple the data
    
    const startTime = Date.now();
    const performanceResults = await targetTypeRepo.getAgreeStatusByUids(performanceIdentifiers);
    const endTime = Date.now();
    
    console.log(`Performance test completed:`);
    console.log(`  Input count: ${performanceIdentifiers.length}`);
    console.log(`  Result count: ${performanceResults.length}`);
    console.log(`  Time taken: ${endTime - startTime}ms`);

    // Test 7: Verify the SQL query structure
    console.log('\n🔍 Testing query structure...');
    if (identifiers.length > 0) {
      // Create a query builder to see the generated SQL
      const queryBuilder = targetTypeRepo.repository.createQueryBuilder('targetType');
      
      const whereConditions = identifiers
        .map((_, index) => `(targetType.uidTarget = :uidTarget${index} AND targetType.uid = :uid${index})`)
        .join(' OR ');

      const parameters: Record<string, string> = {};
      identifiers.forEach((identifier, index) => {
        parameters[`uidTarget${index}`] = identifier.uidTarget;
        parameters[`uid${index}`] = identifier.uid;
      });

      queryBuilder
        .select(['targetType.uidTarget', 'targetType.uid', 'targetType.agree'])
        .where(whereConditions, parameters);

      console.log('Generated SQL:', queryBuilder.getSql());
      console.log('Parameters:', queryBuilder.getParameters());
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await dataSource.destroy();
    console.log('✅ Database connection closed.');
  }
}

testUpdatedAgreeMethod();
